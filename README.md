# 🧮 AI Calculator

A powerful, intelligent calculator that understands natural language and performs advanced mathematical operations including calculus, algebra, and more.

![AI Calculator](https://img.shields.io/badge/AI-Calculator-blue?style=for-the-badge)
![Next.js](https://img.shields.io/badge/Next.js-15.2.4-black?style=flat-square&logo=next.js)
![React](https://img.shields.io/badge/React-19-blue?style=flat-square&logo=react)
![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?style=flat-square&logo=typescript)
![TailwindCSS](https://img.shields.io/badge/TailwindCSS-4.1.9-blue?style=flat-square&logo=tailwindcss)

## ✨ Features

### 🗣️ Natural Language Processing
- **Conversational Input**: Ask questions like "What is 15% of 240?" or "Find the derivative of sin(x)"
- **Smart Intent Recognition**: Automatically detects mathematical operations from natural language
- **Mixed Input Support**: Handles both mathematical expressions and natural language in the same input

### 🧮 Mathematical Capabilities
- **Basic Arithmetic**: Addition, subtraction, multiplication, division, exponents
- **Advanced Functions**: Trigonometric (sin, cos, tan), logarithmic (log, ln), square root
- **Algebraic Operations**: Equation solving, polynomial evaluation
- **Constants**: π (pi), e (Euler's number), and other mathematical constants

### 📐 Calculus Engine
- **Derivatives**: Calculate derivatives of complex functions
- **Integrals**: Both definite and indefinite integration
- **Limits**: Evaluate limits as variables approach specific values
- **Step-by-Step Solutions**: Detailed breakdown of calculus operations

### 💾 Smart History Management
- **Persistent Storage**: IndexedDB-based local storage for calculation history
- **Search & Filter**: Find previous calculations with advanced search
- **Quick Replay**: Re-run previous calculations with one click
- **Export Options**: Copy results and expressions to clipboard

### 🎨 Modern UI/UX
- **Dark/Light Theme**: Automatic theme switching with system preference
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile
- **Interactive Guide**: Comprehensive user guide with examples
- **Real-time Feedback**: Input type detection and validation

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- pnpm (recommended) or npm

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/ai-calculator.git
   cd ai-calculator
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Run the development server**
   ```bash
   pnpm dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Production Build
```bash
pnpm build
pnpm start
```

## 🛠️ Tech Stack

### Core Framework
- **Next.js 15.2.4** - React framework with App Router
- **React 19** - UI library with latest features
- **TypeScript 5** - Type-safe development

### Mathematical Libraries
- **Math.js** - Comprehensive math library for expressions and functions
- **Algebrite** - Computer algebra system for calculus operations

### UI & Styling
- **TailwindCSS 4.1.9** - Utility-first CSS framework
- **Radix UI** - Accessible, unstyled UI components
- **Lucide React** - Beautiful icon library
- **next-themes** - Theme management

### Data & State
- **IndexedDB** - Client-side persistent storage
- **Zod** - Schema validation
- **React Hook Form** - Form state management

## 📖 Usage Examples

### Natural Language Queries
```
"What is the square root of 144?"
"Calculate 15% of 240"
"Find the derivative of x^2 + 3x + 2"
"Integrate sin(x) from 0 to pi"
"What is the limit of (x^2 - 1)/(x - 1) as x approaches 1?"
```

### Mathematical Expressions
```
sqrt(144)
15% * 240
2^10
sin(pi/2)
log(100)
(x^2 - 5*x + 6) = 0
```

## 🔧 Architecture

### Core Components
- **Calculator**: Main input and calculation interface
- **Math Engine**: Expression evaluation and validation
- **Calculus Engine**: Advanced calculus operations
- **NLP Processor**: Natural language understanding
- **History Manager**: Persistent storage and retrieval

### Key Libraries Integration
- **Math.js**: Basic mathematical operations and expression parsing
- **Algebrite**: Symbolic mathematics for calculus operations
- **IndexedDB**: Browser-based persistent storage for history

## ⚠️ Current Limitations

### Mathematical Scope
- **Complex Numbers**: Limited support for complex number operations
- **Matrix Operations**: No matrix algebra capabilities
- **Statistical Functions**: Basic statistics not implemented
- **Graphing**: No plotting or visualization features

### Calculus Limitations
- **Multivariable Calculus**: Only single-variable operations supported
- **Advanced Integration**: Some complex integrals may not be solvable
- **Differential Equations**: No support for solving differential equations

### Technical Constraints
- **Offline Mode**: Requires internet connection for initial load
- **Large Expressions**: Performance may degrade with very complex expressions
- **Browser Compatibility**: Modern browsers required (ES2020+)

## 🚧 Future Improvements

### Planned Features
- **Graphing Calculator**: Interactive function plotting and visualization
- **Matrix Operations**: Linear algebra support with matrix calculations
- **Statistical Analysis**: Descriptive statistics, probability distributions
- **Unit Conversions**: Length, weight, temperature, and other unit conversions
- **Equation Systems**: Solving systems of linear and nonlinear equations

### Enhanced User Experience
- **Voice Input**: Speech-to-text for hands-free calculations
- **LaTeX Rendering**: Beautiful mathematical notation display
- **Collaborative Features**: Share calculations and workspaces
- **Mobile App**: Native iOS and Android applications
- **Offline Support**: Progressive Web App with offline capabilities

### Advanced Mathematical Features
- **3D Graphing**: Three-dimensional function visualization
- **Symbolic Computation**: Enhanced computer algebra system
- **Numerical Methods**: Root finding, optimization, numerical integration
- **Machine Learning**: Pattern recognition in mathematical data

### Developer Experience
- **API Endpoints**: RESTful API for external integrations
- **Plugin System**: Extensible architecture for custom functions
- **Export Formats**: PDF, LaTeX, MathML export options
- **Comprehensive Testing**: Automated testing for all mathematical operations

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Setup
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Commit your changes: `git commit -m 'Add amazing feature'`
5. Push to the branch: `git push origin feature/amazing-feature`
6. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Math.js** - Comprehensive mathematics library
- **Algebrite** - Computer algebra system
- **Radix UI** - Accessible component primitives
- **Vercel** - Deployment and hosting platform

---

**Built with ❤️ for mathematical computing and education**
