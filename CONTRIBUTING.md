# 🤝 Contributing to AI Calculator

Thank you for your interest in contributing to the AI Calculator! We welcome contributions from developers of all skill levels. This guide will help you get started.

## 📋 Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Project Structure](#project-structure)
- [Contributing Guidelines](#contributing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Issue Guidelines](#issue-guidelines)
- [Development Workflow](#development-workflow)
- [Testing](#testing)
- [Code Style](#code-style)

## 📜 Code of Conduct

This project adheres to a code of conduct that we expect all contributors to follow:

- **Be respectful** and inclusive in all interactions
- **Be constructive** when providing feedback
- **Focus on the code**, not the person
- **Help others learn** and grow
- **Report unacceptable behavior** to the maintainers

## 🚀 Getting Started

### Prerequisites

Before contributing, ensure you have:

- **Node.js 18+** installed
- **pnpm** (preferred) or npm package manager
- **Git** for version control
- A **GitHub account**
- Basic knowledge of **React**, **TypeScript**, and **Next.js**

### First-Time Setup

1. **Fork the repository** on GitHub
2. **Clone your fork** locally:
   ```bash
   git clone https://github.com/YOUR_USERNAME/ai-calculator.git
   cd ai-calculator
   ```
3. **Add upstream remote**:
   ```bash
   git remote add upstream https://github.com/anshthakur0999/ai-calculator.git
   ```
4. **Install dependencies**:
   ```bash
   pnpm install
   ```
5. **Start development server**:
   ```bash
   pnpm dev
   ```

## 🏗️ Development Setup

### Environment Configuration

1. **Copy environment template** (if exists):
   ```bash
   cp .env.example .env.local
   ```

2. **Verify installation**:
   ```bash
   pnpm dev
   ```
   Open [http://localhost:3000](http://localhost:3000) to verify everything works.

### Development Tools

- **VS Code** (recommended) with extensions:
  - TypeScript and JavaScript Language Features
  - Tailwind CSS IntelliSense
  - Prettier - Code formatter
  - ESLint

## 📁 Project Structure

```
ai-calculator/
├── app/                    # Next.js App Router pages
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── ui/               # Reusable UI components
│   ├── calculator.tsx    # Main calculator component
│   ├── calculation-history.tsx
│   ├── user-guide.tsx    # Interactive user guide
│   └── ...
├── lib/                  # Core business logic
│   ├── math-engine.ts    # Mathematical operations
│   ├── calculus-engine.ts # Calculus operations
│   ├── nlp-processor.ts  # Natural language processing
│   ├── history-manager.ts # Calculation history
│   ├── types.ts          # TypeScript type definitions
│   └── utils.ts          # Utility functions
├── hooks/                # Custom React hooks
├── types/                # Additional type definitions
├── public/               # Static assets
└── styles/               # Additional stylesheets
```

## 🛠️ Contributing Guidelines

### Types of Contributions

We welcome various types of contributions:

1. **🐛 Bug Fixes**: Fix existing issues or bugs
2. **✨ New Features**: Add new mathematical functions or UI features
3. **📚 Documentation**: Improve README, comments, or guides
4. **🎨 UI/UX Improvements**: Enhance user interface and experience
5. **⚡ Performance**: Optimize code performance
6. **🧪 Testing**: Add or improve tests
7. **♿ Accessibility**: Improve accessibility features

### Before You Start

1. **Check existing issues** to avoid duplicate work
2. **Create an issue** for major changes to discuss the approach
3. **Start small** if you're a first-time contributor
4. **Ask questions** if you're unsure about anything

## 🔄 Pull Request Process

### 1. Create a Feature Branch

```bash
# Update your main branch
git checkout main
git pull upstream main

# Create a new feature branch
git checkout -b feature/your-feature-name
```

### 2. Make Your Changes

- Write clean, readable code
- Follow existing code patterns
- Add comments for complex logic
- Update documentation if needed

### 3. Test Your Changes

```bash
# Run the development server
pnpm dev

# Test your changes manually
# Verify existing functionality still works
```

### 4. Commit Your Changes

```bash
# Stage your changes
git add .

# Commit with a descriptive message
git commit -m "feat: add matrix multiplication support"
```

### 5. Push and Create PR

```bash
# Push to your fork
git push origin feature/your-feature-name
```

Then create a Pull Request on GitHub with:
- **Clear title** describing the change
- **Detailed description** of what was changed and why
- **Screenshots** for UI changes
- **Testing instructions** for reviewers

### 6. Address Review Feedback

- Respond to reviewer comments
- Make requested changes
- Push updates to the same branch

## 🐛 Issue Guidelines

### Reporting Bugs

When reporting bugs, please include:

1. **Clear title** summarizing the issue
2. **Steps to reproduce** the bug
3. **Expected behavior** vs actual behavior
4. **Screenshots** or error messages
5. **Environment details** (browser, OS, etc.)
6. **Mathematical expression** that caused the issue

### Feature Requests

For feature requests, please include:

1. **Problem description** - what need does this address?
2. **Proposed solution** - how should it work?
3. **Alternatives considered** - other approaches you've thought of
4. **Use cases** - specific examples of how it would be used

## 🔧 Development Workflow

### Mathematical Functions

When adding new mathematical functions:

1. **Add to Math Engine** (`lib/math-engine.ts`)
2. **Update NLP Processor** (`lib/nlp-processor.ts`) for natural language support
3. **Add examples** to User Guide (`components/user-guide.tsx`)
4. **Test thoroughly** with various inputs

### UI Components

When creating UI components:

1. **Use TypeScript** with proper type definitions
2. **Follow existing patterns** in the `components/` directory
3. **Use Radix UI** primitives when possible
4. **Ensure accessibility** with proper ARIA labels
5. **Support dark/light themes**

### Calculus Operations

When adding calculus features:

1. **Extend Calculus Engine** (`lib/calculus-engine.ts`)
2. **Add step-by-step explanations**
3. **Handle edge cases** and error conditions
4. **Provide alternative formats** for results

## 🧪 Testing

### Manual Testing

Always test your changes by:

1. **Basic functionality** - ensure core features work
2. **Edge cases** - test with unusual inputs
3. **Error handling** - verify graceful error messages
4. **Cross-browser** - test in different browsers
5. **Responsive design** - test on mobile devices

### Testing Checklist

- [ ] Calculator accepts various input formats
- [ ] Natural language processing works correctly
- [ ] Mathematical operations return correct results
- [ ] History functionality saves and retrieves properly
- [ ] UI is responsive and accessible
- [ ] Dark/light theme switching works
- [ ] Error messages are helpful and clear

## 🎨 Code Style

### TypeScript Guidelines

- Use **strict TypeScript** with proper type annotations
- Prefer **interfaces** over types for object shapes
- Use **const assertions** where appropriate
- Avoid **any** types - use proper typing

### React Guidelines

- Use **functional components** with hooks
- Prefer **named exports** over default exports
- Use **proper dependency arrays** in useEffect
- Handle **loading and error states** appropriately

### Styling Guidelines

- Use **TailwindCSS** utility classes
- Follow **mobile-first** responsive design
- Ensure **dark/light theme** compatibility
- Use **semantic HTML** elements

### Code Formatting

We use Prettier for code formatting. Before committing:

```bash
# Format code (if Prettier is set up)
pnpm format

# Lint code (if ESLint is set up)
pnpm lint
```

## 🏷️ Commit Message Convention

Use clear, descriptive commit messages:

```
feat: add matrix multiplication support
fix: resolve derivative calculation for trigonometric functions
docs: update installation instructions
style: improve calculator button layout
refactor: simplify NLP processing logic
test: add unit tests for calculus engine
```

## 🆘 Getting Help

If you need help:

1. **Check existing issues** and documentation
2. **Create a discussion** on GitHub for questions
3. **Join our community** (if applicable)
4. **Ask in your PR** if you're stuck during development

## 🎉 Recognition

Contributors will be:

- **Listed in README** acknowledgments
- **Credited in release notes** for significant contributions
- **Invited as collaborators** for consistent, quality contributions

---

**Thank you for contributing to AI Calculator! Your efforts help make mathematical computing more accessible to everyone.** 🚀
